package com.pass.hbl.manager.backend.persistence.service.hm.handlers;

import com.pass.hbl.manager.backend.persistence.domain.hm.*;
import com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode;
import com.pass.hbl.manager.backend.persistence.entity.hm.*;
import com.pass.hbl.manager.backend.persistence.entity.shared.SharedLocalization;
import com.pass.hbl.manager.backend.persistence.exception.EntityNotExistException;
import com.pass.hbl.manager.backend.persistence.exception.FormatException;
import com.pass.hbl.manager.backend.persistence.exception.InvalidOperationException;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmAwardRepository;
import com.pass.hbl.manager.backend.persistence.repository.hm.HmUserAwardRepository;
import com.pass.hbl.manager.backend.persistence.service.admin.LogMessageService;
import com.pass.hbl.manager.backend.persistence.service.hm.LeagueService;
import com.pass.hbl.manager.backend.persistence.service.hm.UserProfileService;
import com.pass.hbl.manager.backend.persistence.service.shared.TransactionHandler;
import com.pass.hbl.manager.backend.persistence.util.Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.LockModeType;
import java.time.LocalDateTime;
import java.util.*;

import static com.pass.hbl.manager.backend.persistence.dto.hm.AwardCode.*;
import static com.pass.hbl.manager.backend.persistence.util.Constants.*;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static java.util.Optional.empty;
import static java.util.stream.Collectors.toSet;

@Slf4j
@Service
@Transactional
public class AwardCheckingHandler {

    private final HmAwardRepository hmAwardRepository;
    private final HmUserAwardRepository hmUserAwardRepository;

    private final UserProfileService userProfileService;
    private final LeagueService leagueService;

    private final TransactionHandler transactionHandler;
    private final LeagueMembershipHandler leagueMembershipHandler;
    private final LeagueScoreHandler leagueScoreHandler;
    private final LogMessageService logMessageService;


    List<HmAward> allAwards = new ArrayList<>();

    @EventListener(ApplicationReadyEvent.class)
    @Transactional(readOnly = true)
    public void init() throws InvalidOperationException, FormatException {
        allAwards = Util.toStream(hmAwardRepository.findAll()).toList();
        log.info("AwardCheckingHandler: all hmAwards initialized. count = " + allAwards.size());
    }

    public AwardCheckingHandler(HmAwardRepository hmAwardRepository, HmUserAwardRepository hmUserAwardRepository, UserProfileService userProfileService, LeagueService leagueService, TransactionHandler transactionHandler, LeagueMembershipHandler leagueMembershipHandler, LeagueScoreHandler leagueScoreHandler, LogMessageService logMessageService) {
        this.hmAwardRepository = hmAwardRepository;
        this.hmUserAwardRepository = hmUserAwardRepository;
        this.userProfileService = userProfileService;
        this.leagueService = leagueService;
        this.transactionHandler = transactionHandler;
        this.leagueMembershipHandler = leagueMembershipHandler;
        this.leagueScoreHandler = leagueScoreHandler;
        this.logMessageService = logMessageService;
    }

    @Lock(LockModeType.PESSIMISTIC_WRITE)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<HmLeagueMemberAwardsDO> checkRoundScoreAwardsByLeague(List<HmUserProfile> userCache, Map.Entry<UUID, List<HmLeagueMembershipScoreDO>> entry, Optional<UUID> matchDayWinnerOptional, HmRound round) throws FormatException {
        UUID leagueId = entry.getKey();
        List<HmLeagueMemberAwardsDO> result = new ArrayList<>();
        HmLeague league;
        try {
            league = leagueService.getByIdInNewTransaction(leagueId);
        } catch (EntityNotExistException e) {
            String process = "assignMatchDayAwards:checkRoundScoreAwardsByLeague";
            log.error(process + " league [" + leagueId + "] not found. Skipping");
            logMessageService.logException(process, "league [" + leagueId + "] not found. Skipping", e);
            return emptyList();
        }
        List<HmLeagueMembershipScoreDO> leagueMemberships = entry.getValue();
        for (HmLeagueMembershipScoreDO leagueMembership : leagueMemberships) {
            UUID userId = leagueMembership.getUserId();
            boolean isMatchDayWinner = matchDayWinnerOptional.isPresent() && Objects.equals(matchDayWinnerOptional.get(), userId);
            Pair<Integer, List<HmAwardDescriptionDO>> experiencePointsAwardsPair = getExperiencePointsByRoundScoreAwards(league, userCache, leagueMembership, isMatchDayWinner, round);
            int experiencePointsToAdd = experiencePointsAwardsPair.getKey();
            List<HmAwardDescriptionDO> awardDescriptions = experiencePointsAwardsPair.getValue();
            if (!awardDescriptions.isEmpty()) {
                result.add(new HmLeagueMemberAwardsDO(userId, leagueId, league.getName(), experiencePointsToAdd, awardDescriptions));
            }
            log.info("assignMatchDayAwards: checkRoundScoreAwardsByLeague experience points = [" + experiencePointsToAdd + "] to add for user [" + userId + "] in league [" + leagueId + "]");
        }
        return result;
    }

    public Pair<Integer, List<HmAwardDescriptionDO>> getExperiencePointsByRoundScoreAwards(HmLeague league, List<HmUserProfile> userCache, HmLeagueMembershipScoreDO leagueMembershipScoreDO, boolean isMatchDayWinner, HmRound round) throws FormatException {

        // check point picker award
        Triple<Integer, Integer, List<HmAwardDescriptionDO>> moneyAndXpByPointPickerAward = getMoneyAndExperiencePointsByPointPickerAward(league, userCache, leagueMembershipScoreDO, round);
        int moneyByPointPickerAward = moneyAndXpByPointPickerAward.getLeft();
        int experiencePointsByPointPickerAward = moneyAndXpByPointPickerAward.getMiddle();
        List<HmAwardDescriptionDO> awardDescriptionList = new ArrayList<>(moneyAndXpByPointPickerAward.getRight());

        // add match day winner award if member is the match day winner
        int moneyByMatchDayWinnerAward = 0;
        int experiencePointsByMatchDayWinnerAward = 0;
        if (isMatchDayWinner) {
            //add or update match day winner award
            Triple<Integer, Integer, Optional<HmAwardDescriptionDO>> moneyAndXpByMatchDayWinnerAward = getMoneyAndExperiencePointsByMatchDayWinnerAward(league, userCache, leagueMembershipScoreDO, round);
            moneyByMatchDayWinnerAward = moneyAndXpByMatchDayWinnerAward.getLeft();
            experiencePointsByMatchDayWinnerAward = moneyAndXpByMatchDayWinnerAward.getMiddle();
            moneyAndXpByMatchDayWinnerAward.getRight().ifPresent(awardDescriptionList::add);
        }

        int moneyToAdd = moneyByPointPickerAward + moneyByMatchDayWinnerAward;
        if (moneyToAdd > 0) {
            leagueService.addMoneyToUserBalance(leagueMembershipScoreDO.getLeagueId(), leagueMembershipScoreDO.getUserId(), moneyToAdd);
        }
        int experiencePointsToAdd = experiencePointsByPointPickerAward + experiencePointsByMatchDayWinnerAward;
        return Pair.of(experiencePointsToAdd, awardDescriptionList);
    }

    public Pair<Integer, List<HmAwardDescriptionDO>> getMoneyByTransferMarketBuyerAwards(UUID transferMarketId, HmLeague league, HmUserProfile user, AwardCheckingDo awardCheckingDo) {

        // WIP, currently inactive: check if it is the first transfer of the buyer over all leagues
        int moneyByFirstTransferAward = 0;

        // 1- check if the buyer is a transfer king in league.
        Triple<Integer, Integer, List<HmAwardDescriptionDO>> moneyAndXpByTransferKingAward = getMoneyAndExperiencePointsByTransferKingAward(transferMarketId, league, user, awardCheckingDo.getTransfersInLeagueCount(), awardCheckingDo.getLocale());
        int moneyByTransferKingAward = moneyAndXpByTransferKingAward.getLeft();
        int experiencePointsByTransferKingAward = moneyAndXpByTransferKingAward.getMiddle();
        List<HmAwardDescriptionDO> assignedAwards = new ArrayList<>(moneyAndXpByTransferKingAward.getRight());

        // 2- check if the buyer has made a deal.
        Triple<Integer, Integer, List<HmAwardDescriptionDO>> moneyAndXpByDealMakerAward = getMoneyAndExperiencePointsByDealMakerAward(transferMarketId, league, user, awardCheckingDo.getBidValue(), awardCheckingDo.getLocale());
        int moneyByDealMakerAward = moneyAndXpByDealMakerAward.getLeft();
        int experiencePointsByDealMakerAward = moneyAndXpByDealMakerAward.getMiddle();
        assignedAwards.addAll(moneyAndXpByDealMakerAward.getRight());

        // 3- check if the buyer has a full bench.
        Triple<Integer, Integer, List<HmAwardDescriptionDO>> moneyAndXpByFullBenchAward = getMoneyAndExperiencePointsByFullBenchAward(transferMarketId, league, user, awardCheckingDo.getTeamSize(), awardCheckingDo.getMaxTeamSize(), awardCheckingDo.getLocale());
        int moneyByFullBenchAward = moneyAndXpByFullBenchAward.getLeft();
        int experiencePointsByFullBenchAward = moneyAndXpByFullBenchAward.getMiddle();
        assignedAwards.addAll(moneyAndXpByFullBenchAward.getRight());

        int newExperiencePoints = user.getExperiencePoints() + experiencePointsByTransferKingAward
                + experiencePointsByDealMakerAward + experiencePointsByFullBenchAward;

        // update the experience points by user with all new xps gained in the awards
        if (newExperiencePoints > 0) {
            userProfileService.updateExperiencePointsAndLevelByUser(user.getId(), newExperiencePoints);
        }

        int moneyToAddByAwards = moneyByFirstTransferAward + moneyByTransferKingAward + moneyByDealMakerAward + moneyByFullBenchAward;
        return Pair.of(moneyToAddByAwards, assignedAwards);
    }

    public Pair<Integer, List<HmAwardDescriptionDO>> getMoneyByTransferMarketSellerAwards(UUID transferMarketId, HmLeague league, HmUserProfile user, AwardCheckingDo awardCheckingDo) {

        // WIP, currently inactive: check if it is the first transfer of the buyer over all leagues
        int moneyByFirstTransferAward = 0;

        // check if the Seller is a transfer king in league.
        Triple<Integer, Integer, List<HmAwardDescriptionDO>> moneyAndXpByTransferKingAward = getMoneyAndExperiencePointsByTransferKingAward(transferMarketId, league, user, awardCheckingDo.getTransfersInLeagueCount(), awardCheckingDo.getLocale());
        int moneyByTransferKingAward = moneyAndXpByTransferKingAward.getLeft();
        int experiencePointsByTransferKingAward = moneyAndXpByTransferKingAward.getMiddle();
        List<HmAwardDescriptionDO> assignedAwards = new ArrayList<>(moneyAndXpByTransferKingAward.getRight());

        int newExperiencePoints = user.getExperiencePoints() + experiencePointsByTransferKingAward;

        if (newExperiencePoints > 0) {
            userProfileService.updateExperiencePointsAndLevelByUser(user.getId(), newExperiencePoints);
        }

        int moneyToAddByAwards = moneyByFirstTransferAward + moneyByTransferKingAward;
        return Pair.of(moneyToAddByAwards, assignedAwards);
    }

    public Triple<Integer, Integer, List<HmAwardDescriptionDO>> getMoneyAndExperiencePointsByDealMakerAward(UUID transferMarketId, HmLeague league, HmUserProfile user, int bid, Locale locale) {
        if (bid >= DEALMAKER_GOLD_MIN_BID) {
            // make sure that user who is earning the gold award has already silver and bronze award
            return doGetMoneyAndExperiencePointsByAwards(transferMarketId, List.of(DEALMAKER_GOLD, DEALMAKER_SILVER, DEALMAKER_BRONZE), user, league, locale);
        } else if (bid >= DEALMAKER_SILVER_MIN_BID) {
            // make sure that user who is earning the silver award has already bronze award
            return doGetMoneyAndExperiencePointsByAwards(transferMarketId, List.of(DEALMAKER_SILVER, DEALMAKER_BRONZE), user, league, locale);
        } else if (bid >= DEALMAKER_BRONZE_MIN_BID) {
            return doGetMoneyAndExperiencePointsByAwards(transferMarketId, List.of(DEALMAKER_BRONZE), user, league, locale);
        }
        return Triple.of(0,0, emptyList());
    }

    public Triple<Integer, Integer, List<HmAwardDescriptionDO>> getMoneyAndExperiencePointsByTransferKingAward(UUID transferMarketId, HmLeague league, HmUserProfile user, int countUserTransfers, Locale locale) {
        log.info("getMoneyByTransferKingAward:countUserTransfers = " + countUserTransfers + " for user [" + user.getId() +
                "] in league [" + league.getId() + "] and TransferMarketId [" + transferMarketId + "]");
        if (countUserTransfers >= TRANSFER_KING_GOLD_MIN) {
            // make sure that user who is earning the gold award has already silver and bronze award
            return doGetMoneyAndExperiencePointsByAwards(transferMarketId, List.of(TKING_GOLD, TKING_SILVER, TKING_BRONZE) , user, league, locale);
        } else if (countUserTransfers >= TRANSFER_KING_SILVER_MIN) {
            // make sure that user who is earning the silver award has already bronze award
            return doGetMoneyAndExperiencePointsByAwards(transferMarketId, List.of(TKING_SILVER, TKING_BRONZE) , user, league, locale);
        } else if (countUserTransfers >= TRANSFER_KING_BRONZE_MIN) {
            return doGetMoneyAndExperiencePointsByAwards(transferMarketId, List.of(TKING_BRONZE) , user, league, locale);
        }
        return Triple.of(0,0, emptyList());
    }

    public Triple<Integer, Integer, List<HmAwardDescriptionDO>> getMoneyAndExperiencePointsByPointPickerAward(HmLeague league, List<HmUserProfile> userCache,
                                                                                                              HmLeagueMembershipScoreDO leagueMembershipScoreDO, HmRound round) throws FormatException {
        int score = leagueMembershipScoreDO.getScore();
        if (score >= POINT_PICKER_GOLD_MIN) {
            // make sure that user who is earning the gold award has already silver and bronze award
            return doGetMoneyAndExperiencePointsByPointPickerAwards(league, userCache, List.of(PICKER_GOLD, PICKER_SILVER, PICKER_BRONZE), leagueMembershipScoreDO.getUserId(), round);
        } else if (score >= POINT_PICKER_SILVER_MIN) {
            // make sure that user who is earning the silver award has already bronze award
            return doGetMoneyAndExperiencePointsByPointPickerAwards(league, userCache, List.of(PICKER_SILVER, PICKER_BRONZE), leagueMembershipScoreDO.getUserId(), round);
        } else if (score >= POINT_PICKER_BRONZE_MIN) {
            return doGetMoneyAndExperiencePointsByPointPickerAwards(league, userCache, List.of(PICKER_BRONZE), leagueMembershipScoreDO.getUserId(), round);
        }
        return Triple.of(0,0, emptyList());
    }

    public Triple<Integer, Integer, List<HmAwardDescriptionDO>> getMoneyAndExperiencePointsByFullBenchAward(UUID transferMarketId, HmLeague league, HmUserProfile user, int userTeamSize, int maxTeamSize, Locale locale) {
        log.info("getMoneyByFullBenchAward:userTeamSize = " + userTeamSize + " for user [" + user.getId() +
                "] in league [" + league.getId() + "] and TransferMarketId [" + transferMarketId + "]");
        if (userTeamSize == maxTeamSize) {
            return doGetMoneyAndExperiencePointsByAwards(transferMarketId, List.of(FULL_BENCH), user, league, locale);
        }
        return Triple.of(0,0, emptyList());
    }


    public Triple<Integer, Integer, Optional<HmAwardDescriptionDO>> getMoneyAndExperiencePointsByMatchDayWinnerAward(HmLeague league, List<HmUserProfile> userCache,
                                                                                   HmLeagueMembershipScoreDO leagueMembershipScoreDO, HmRound round) throws FormatException {
        Optional<HmAward> awardOptional = getAwardByCode(MATCHDAY_WINNER);
        if (awardOptional.isPresent()) {
            HmAward award = awardOptional.get();
            UUID userId = leagueMembershipScoreDO.getUserId();
            UUID leagueId = leagueMembershipScoreDO.getLeagueId();
            Optional<String> userAwardIdOptional = hmUserAwardRepository.findIdByUserIdAndAwardIdAndLeagueId(userId, award.getId(), leagueId);
            boolean userAwardExists = userAwardIdOptional.isPresent();
            if (userAwardExists) {
                hmUserAwardRepository.incrementNumberOfAchievements(UUID.fromString(userAwardIdOptional.get()), round.getId());
                log.info("award with code [" + MATCHDAY_WINNER + "] and id [" + award.getId() + "] found for league [" + leagueId
                        + "] and user [" + userId + "]. Number of achievements incremented.");
            } else {
                // get user profile from cache since the user could have match day awards in multiple leagues
                Optional<HmUserProfile> userProfileOptional = getFromUserCache(userCache, userId);
                if (userProfileOptional.isPresent()) {
                    HmUserProfile user = userProfileOptional.get();
                    HmUserAward userAward = hmUserAwardRepository.save(new HmUserAward(award, user, league, round));
                    log.info("user award with code [" + MATCHDAY_WINNER + "] and id[" + userAward.getId()
                            + "] assigned to user [" + user.getId() + "] in league [" + league.getId() + "]");
                    // Experience points and money should be updated with the auction bid to avoid multiple transactions
                    int experiencePointsByAward = nonNull(award.getExperiencePoints()) ? award.getExperiencePoints() : 0;
                    int money = nonNull(award.getMoney()) ? award.getMoney() : 0;

                    // get award description by locale: relevant for sending user notification
                    Locale locale = Util.getLocaleByLanguageTag(user.getAppLanguage());
                    return Triple.of(money, experiencePointsByAward, Optional.of(getAwardDescriptionByLocale(award, locale, MATCHDAY_WINNER)));
                }
            }
        } else {
            log.info("award with code [" + MATCHDAY_WINNER + "] not found. Skipping..");
        }
        return Triple.of(0, 0, empty());
    }

    public Optional<Pair<UUID, HmAwardDescriptionDO>> checkFullHouseAward(HmLeague league, int leagueSize) throws EntityNotExistException, FormatException {
        if (leagueSize >= leagueMembershipHandler.getMaxLeagueSize()) {
            UUID ownerId = leagueService.getOwnerIdByLeagueId(league.getId());
            HmUserProfile owner = userProfileService.getByIdInNewTransaction(ownerId);
            Locale locale = Util.getLocaleByLanguageTag(owner.getAppLanguage());
            Pair<Integer, Optional<HmAwardDescriptionDO>> moneyByAwardPair = doGetMoneyByAward(FULL_HOUSE, owner, league, locale);
            int moneyByFullHouseAward = moneyByAwardPair.getKey();
            if (moneyByFullHouseAward > 0) {
                leagueService.addMoneyToUserBalance(league.getId(), ownerId, moneyByFullHouseAward);
                // check if user is currently negative on round closing
                leagueScoreHandler.checkUserRoundScore(owner, league, LocalDateTime.now());
            }
            Optional<HmAwardDescriptionDO> awardDescriptionOptional = moneyByAwardPair.getValue();
            if (awardDescriptionOptional.isPresent()) {
                return Optional.of(Pair.of(ownerId, awardDescriptionOptional.get()));
            }
        }
        return empty();
    }

    public Optional<Pair<UUID, HmAwardDescriptionDO>> checkStart7Award(UUID userId, UUID leagueId, int lineupSize) throws EntityNotExistException, FormatException {
        if (lineupSize == 7) {
            HmUserProfile user = userProfileService.getByIdInNewTransaction(userId);
            HmLeague league = leagueService.getByIdInNewTransaction(leagueId);
            Locale locale = Util.getLocaleByLanguageTag(user.getAppLanguage());
            Pair<Integer, Optional<HmAwardDescriptionDO>> moneyByAwardPair = doGetMoneyByAward(START7, user, league, locale);
            int moneyByStart7Award = moneyByAwardPair.getKey();
            if (moneyByStart7Award > 0) {
                leagueService.addMoneyToUserBalance(league.getId(), userId, moneyByStart7Award);
                // check if user is currently negative on round closing
                leagueScoreHandler.checkUserRoundScore(user, league, LocalDateTime.now());
            }
            Optional<HmAwardDescriptionDO> awardDescriptionOptional = moneyByAwardPair.getValue();
            if (awardDescriptionOptional.isPresent()) {
                return Optional.of(Pair.of(userId, awardDescriptionOptional.get()));
            }
        }
        return empty();
    }

    public Optional<HmAwardDescriptionDO> checkImageRightAward(HmUserProfile user) throws FormatException {
        UUID userId = user.getId();
        // double check if user image exists
        if(Objects.nonNull(user.getPicture())) {
            Optional<HmAward> awardOptional = getAwardByCode(IMAGE_RIGHT);
            if (awardOptional.isEmpty()) {
                log.info("Award with code = IMAGE_RIGHT does not exist. Skipping..");
            } else {
                HmAward award = awardOptional.get();
                boolean userAwardExists = hmUserAwardRepository.findIdByUserIdAndAwardId(userId, award.getId()).isPresent();
                if (userAwardExists) {
                    log.info("award with code [IMAGE_RIGHT] and id [" + award.getId() + "] found for user [" + user.getId() + "] . Skipping..");
                } else {
                    HmUserAward userAward = hmUserAwardRepository.save(new HmUserAward(award, user));
                    log.info("user award with code [IMAGE_RIGHT] and id[" + userAward.getId() + "] assigned to user [" + user.getId() + "]");
                    // update only the experience points
                    if (nonNull(award.getExperiencePoints())) {
                        int newExperiencePoints = user.getExperiencePoints() + award.getExperiencePoints();
                        userProfileService.updateExperiencePointsAndLevelByUser(user.getId(), newExperiencePoints);
                    }
                    Locale locale = Util.getLocaleByLanguageTag(user.getAppLanguage());
                    getAwardDescriptionByLocale(award, locale, IMAGE_RIGHT);
                    return Optional.of(getAwardDescriptionByLocale(award, locale, IMAGE_RIGHT));
                }
            }
        }
        return empty();
    }

    private Pair<Integer, Optional<HmAwardDescriptionDO>> doGetMoneyByAward(AwardCode awardCode, HmUserProfile user, HmLeague league, Locale locale) {
        Optional<HmAward> awardOptional = getAwardByCode(awardCode);
        if (awardOptional.isPresent()) {
            HmAward award = awardOptional.get();
            boolean userAwardExists = hmUserAwardRepository.findIdByUserIdAndAwardIdAndLeagueId(user.getId(), award.getId(), league.getId()).isPresent();
            if (userAwardExists) {
                log.info("award with code [" + awardCode + "] and id [" + award.getId() + "] found for league [" + league.getId() + "] and user [" + user.getId() + "] . Skipping..");
                return Pair.of(0, empty());
            } else {
                HmUserAward userAward = hmUserAwardRepository.save(new HmUserAward(award, user, league));
                log.info("user award with code [" + awardCode + "] and id[" + userAward.getId() + "] assigned to user [" + user.getId() + "] in league [" + league.getId() + "]");
                // update only the experience points, money should be updated with the auction bid to avoid double transactions
                if (nonNull(award.getExperiencePoints())) {
                    int newExperiencePoints = user.getExperiencePoints() + award.getExperiencePoints();
                    userProfileService.updateExperiencePointsAndLevelByUser(user.getId(), newExperiencePoints);
                }
                int moneyByAward = nonNull(award.getMoney()) ? award.getMoney() : 0;
                return Pair.of(moneyByAward, Optional.of(getAwardDescriptionByLocale(award, locale, awardCode)));
            }
        } else {
            log.info("award with code [" + awardCode + "] not found. Skipping..");
            return Pair.of(0, empty());
        }
    }


    private Triple<Integer, Integer, List<HmAwardDescriptionDO>> doGetMoneyAndExperiencePointsByAwards(UUID transferMarketId, List<AwardCode> awardCodes, HmUserProfile user, HmLeague league, Locale locale) {
        // Get the list of award stages that the user should have.
        // Example user who is earning transfer king gold should also have earned silver and bronze.
        Set<HmAward> requestedAwards = getAwardsByCodes(awardCodes);
        Set<UUID> requestedAwardsIds = requestedAwards.stream().map(HmAward::getId).collect(toSet());

        // 1- get the list of user awards (or single award) that the user already have
        List<String> existingAwardsIds = hmUserAwardRepository.findAwardIdsByUserIdAndAwardIdAndLeagueId(user.getId(), requestedAwardsIds, league.getId());

        // 2- check if the awards exists otherwise assign it
        int totalMoney = 0;
        int totalExperiencePoints = 0;
        List<HmAwardDescriptionDO> awardDescriptionList = new ArrayList<>();
        for (HmAward award : requestedAwards) {
            boolean userAwardExists = existingAwardsIds.stream().anyMatch(existingAwardsId -> Objects.equals(existingAwardsId, award.getId().toString()));
            AwardCode awardCode = award.getCode();
            if (userAwardExists) {
                String transferMarketIdMessage = isNull(transferMarketId) ? "" : "in TransferMarketId [" + transferMarketId + "]";
                log.info("award with code [" + awardCode + "] and id [" + award.getId() + "] found for league [" + league.getId()
                        + "] and user [" + user.getId() + "] " + transferMarketIdMessage + ". Skipping..");
            } else {
                HmUserAward userAward = hmUserAwardRepository.save(new HmUserAward(award, user, league));
                String transferMarketIdMessage = isNull(transferMarketId) ? "" : "and TransferMarketId [" + transferMarketId + "]";
                log.info("user award with code [" + awardCode + "] and id[" + userAward.getId()
                        + "] assigned to user [" + user.getId() + "] in league [" + league.getId() + "] " + transferMarketIdMessage);
                // get money and experience points to add by getting this award
                int experiencePointsByAward = nonNull(award.getExperiencePoints()) ? award.getExperiencePoints() : 0;
                int money = nonNull(award.getMoney()) ? award.getMoney() : 0;
                totalExperiencePoints = totalExperiencePoints + experiencePointsByAward;
                totalMoney = totalMoney + money;

                // get the language specific award description that should be sent as firebase message
                awardDescriptionList.add(getAwardDescriptionByLocale(award, locale, awardCode));
            }
        }
        return Triple.of(totalMoney, totalExperiencePoints, awardDescriptionList);
    }

    private Triple<Integer, Integer, List<HmAwardDescriptionDO>> doGetMoneyAndExperiencePointsByPointPickerAwards(HmLeague league, List<HmUserProfile> userCache, List<AwardCode> awardCodes, UUID userId, HmRound round) throws FormatException {
        // 1- Get the list of awards that the user should have. Example: user who is earning point picker gold should also have earned silver and bronze.
        Set<HmAward> requestedAwards = getAwardsByCodes(awardCodes);
        Set<UUID> requestedAwardsIds = requestedAwards.stream().map(HmAward::getId).collect(toSet());

        // 2- get the list of user awards (or single award) that the user already have
        List<String> existingAwardsIds = hmUserAwardRepository.findAwardIdsByUserIdAndAwardIdAndLeagueId(userId, requestedAwardsIds, league.getId());

        // 3- check if the awards exists otherwise assign it
        int totalMoney = 0;
        int totalExperiencePoints = 0;
        List<HmAwardDescriptionDO> awardDescriptionList = new ArrayList<>();

        // get user profile from cache since the user could have point picker awards in multiple leagues
        Optional<HmUserProfile> userProfileOptional = empty();
        for (HmAward award : requestedAwards) {
            boolean userAwardExists = existingAwardsIds.stream().anyMatch(existingAwardsId -> Objects.equals(existingAwardsId, award.getId().toString()));
            AwardCode awardCode = award.getCode();
            if (userAwardExists) {
                log.info("award with code [" + awardCode + "] and id [" + award.getId() + "] found for league [" + league.getId() + "] and user [" + userId + "]. Skipping..");
            } else {
                // initialize user only one time
                if (userProfileOptional.isEmpty()) {
                    userProfileOptional = getFromUserCache(userCache, userId);
                }
                if (userProfileOptional.isPresent()) {
                    HmUserProfile user = userProfileOptional.get();
                    HmUserAward userAward = hmUserAwardRepository.save(new HmUserAward(award, user, league, round));
                    log.info("user award with code [" + awardCode + "] and id[" + userAward.getId() + "] assigned to user [" + userId + "] in league [" + league.getId() + "]");
                    // get money and experience points to add by getting this award
                    int experiencePointsByAward = nonNull(award.getExperiencePoints()) ? award.getExperiencePoints() : 0;
                    int money = nonNull(award.getMoney()) ? award.getMoney() : 0;
                    totalExperiencePoints = totalExperiencePoints + experiencePointsByAward;
                    totalMoney = totalMoney + money;
                    // get relevant data for sending user notification
                    awardDescriptionList.add(getAwardDescriptionByLocale(award, Util.getLocaleByLanguageTag(user.getAppLanguage()), awardCode));
                }
            }
        }
        return Triple.of(totalMoney, totalExperiencePoints, awardDescriptionList);
    }

    public Optional<HmUserProfile> getFromUserCache(List<HmUserProfile> userCache, UUID userId) {
        HmUserProfile user;
        // try to find the user from cache, iterate from tempUserCache in order to avoid ConcurrentModificationException
        List<HmUserProfile> tempUserCache = new ArrayList<>(userCache);
        Optional<HmUserProfile> userProfileOptional = tempUserCache.stream().filter(u -> Objects.equals(u.getId(), userId)).findFirst();
        if (userProfileOptional.isPresent()) {
            return userProfileOptional;
        } else {
            // if not found in cache try to find it in the database
            try {
                user = userProfileService.getByIdInNewTransaction(userId);
                userCache.add(user);
                return Optional.of(user);
            } catch (EntityNotExistException e) {
                String process = "assignMatchDayAwards:checkRoundScoreAwardsByLeague";
                log.error(process + " user [" + userId + "] not found. Skipping");
                logMessageService.logException(process, "user [" + userId + "] not found. Skipping", e);
                return empty();
            }
        }
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public Map<AwardCode,HmAward> getAwardsByCodeIn(Set<AwardCode> codes) {
        Map<AwardCode,HmAward> awardsMap = new HashMap<>();
        for (AwardCode code : codes) {
            getAwardByCode(code).ifPresent(award -> awardsMap.put(code, award));
        }
        return awardsMap;
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public Set<UUID> getLeagueIdsByLastRoundIdAndAwardIdIn(List<UUID> awardsIds, UUID lastRoundId) {
        return hmUserAwardRepository.findLeagueIdsByLastRoundIdAndAwardIdIn(lastRoundId, awardsIds).stream()
                .map(UUID::fromString).collect(toSet());
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<HmUserAwardDO> getUserAwardByLastRoundIdAndAwardIdIn(List<UUID> awardsIds, UUID lastRoundId) {
        return hmUserAwardRepository.findUserAwardByLastRoundIdAndAwardIdIn(lastRoundId, awardsIds);
    }

    private Optional<HmAward> getAwardByCode(AwardCode code) {
        Optional<HmAward> awardOptional = allAwards.stream().filter(hmAward -> Objects.equals(hmAward.getCode().name(), code.name())).findFirst();
        // try to get the award from cache
        if (awardOptional.isPresent()) {
            return awardOptional;
        } else {
            // if not found, search it from the database
            log.info("AwardService: award with code = " + code + " not found in cache, it will be retrieved from the database.");
            return transactionHandler.runInNewTransactionReadOnly(() -> hmAwardRepository.findByCode(code).stream().findFirst());
        }
    }

    @NotNull
    private HmAwardDescriptionDO getAwardDescriptionByLocale(HmAward award, Locale locale, AwardCode awardCode) {
        Optional<SharedLocalization> localizationOptional = award.getDescriptions().stream().filter(localization ->
                Objects.equals(localization.getLocale().toString(), locale.toString()) && Objects.equals(localization.getKey(), AWARD_KEY_NAME)).findFirst();
        String awardName = localizationOptional.isPresent() ? localizationOptional.get().getValue() : awardCode.name();
        return new HmAwardDescriptionDO(awardCode, awardName, award.getPicture(), locale, award.getMoney());
    }

    @NotNull
    private Set<HmAward> getAwardsByCodes(List<AwardCode> awardCodes) {
        return awardCodes.stream().map(code -> {
            Optional<HmAward> awardByCodeOptional = getAwardByCode(code);
            if (awardByCodeOptional.isEmpty()) {
                log.info("award with code [" + code + "] not found. Skipping..");
                return null;
            }
            return awardByCodeOptional.get();
        }).filter(Objects::nonNull).collect(toSet());
    }


    //WIP: first transfer award is temporary disabled
    /*public Pair<Integer, Integer> getMoneyAndExperiencePointsByFirstTransferAward(UUID transferMarketId, HmUserProfile user, HmLeague league) {
        // assign first transfer to users, if they have countUserTransfers > 1 and still not have first-transfer award
        return doGetMoneyAndExperiencePointsByAward(transferMarketId, FIRST_TRANSFER, user, league);
     }*/
}
